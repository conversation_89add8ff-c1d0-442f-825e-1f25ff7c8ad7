# GPU RDMA带宽测量工具改造技术指南

## 1. 程序流程总结

### 1.1 当前程序架构概览

现有程序采用客户端-服务器架构，基于NVIDIA DOCA框架实现GPU直接RDMA通信。

#### 核心组件结构
```
gpunetio_rdma_client_server_write_main.c  # 主程序入口和参数解析
├── rdma_common.h/c                       # RDMA资源管理和连接建立
├── host/gpunetio_rdma_client_server_write_sample.c  # 主要业务逻辑
└── device/gpunetio_rdma_client_server_write_kernel.cu  # CUDA内核实现
```

### 1.2 完整执行流程

#### 服务器端执行流程
1. **初始化阶段**
   - 解析命令行参数 (`main()` → `doca_argp_start()`)
   - 创建RDMA资源 (`create_rdma_resources()`)
   - 获取GPU RDMA句柄 (`doca_rdma_get_gpu_handle()`)

2. **连接建立阶段**
   - 建立OOB连接 (`oob_connection_server_setup()`)
   - 导出RDMA连接详情 (`doca_rdma_export()`)
   - 与客户端交换连接信息

3. **内存管理阶段**
   - 分配GPU内存 (`doca_gpu_mem_alloc()`)
   - 创建内存映射 (`create_mmap()`)
   - 创建缓冲区数组 (`create_buf_arr_on_gpu()`)
   - 交换内存映射信息

4. **CUDA执行阶段**
   - 启动CUDA流 (`cudaStreamCreateWithFlags()`)
   - 执行服务器内核 (`kernel_write_server()`)
   - 同步等待完成 (`cudaStreamSynchronize()`)

5. **清理阶段**
   - 销毁内存资源 (`destroy_memory_local_remote_server()`)
   - 销毁RDMA资源 (`destroy_rdma_resources()`)

#### 客户端执行流程
1. **初始化和连接** (与服务器类似)
2. **内存管理** (分配本地缓冲区，接收远程内存映射)
3. **CUDA执行**
   - 分配退出标志内存
   - 执行客户端内核 (`kernel_write_client()`)
   - 设置退出标志终止内核
4. **清理资源**

### 1.3 关键模块交互关系

```
主程序 → RDMA资源管理 → GPU内存管理 → CUDA内核执行
  ↓           ↓              ↓            ↓
参数解析   连接建立      内存映射      RDMA操作
  ↓           ↓              ↓            ↓
配置验证   OOB通信      缓冲区管理    性能测量
```

## 2. 改造计划概述

### 2.1 可复用模块列表

#### 2.1.1 RDMA连接管理模块 (`rdma_common.c`)
**复用原因**: 连接建立机制完善，支持多种连接方式
- `create_rdma_resources()` - RDMA资源初始化
- `destroy_rdma_resources()` - 资源清理
- `oob_connection_*()` - 带外连接管理
- RDMA-CM回调函数集

#### 2.1.2 GPU设备管理模块
**复用原因**: GPU设备初始化和管理逻辑完整
- GPU设备发现和初始化
- GPU内存分配接口
- CUDA流管理

#### 2.1.3 内存映射管理模块
**复用原因**: GPU内存和RDMA内存映射机制成熟
- `create_mmap()` - 内存映射创建
- `create_buf_arr_on_gpu()` - GPU缓冲区数组创建
- 内存导出和导入机制

#### 2.1.4 参数解析框架
**复用原因**: DOCA ARGP框架扩展性强
- 基础参数解析结构
- 回调函数机制
- 参数验证逻辑

### 2.2 需要修改的模块

#### 2.2.1 主程序流程 (`*_main.c`, `*_sample.c`)
**修改要点**:
- 将演示逻辑改为测试循环逻辑
- 添加性能测量代码
- 支持多种测试模式选择
- 集成结果输出功能

#### 2.2.2 CUDA内核逻辑 (`*_kernel.cu`)
**修改要点**:
- 移除演示性的数据操作
- 实现纯RDMA性能测试
- 添加时间测量功能
- 支持可配置的消息大小和迭代次数

#### 2.2.3 内存分配策略
**修改要点**:
- 从固定大小改为动态可配置
- 支持多种消息大小测试
- 优化内存对齐和访问模式

#### 2.2.4 配置结构体扩展
**修改要点**:
- 扩展`rdma_config`结构体
- 添加测试相关参数
- 支持输出格式配置

### 2.3 需要新增的模块

#### 2.3.1 性能测量引擎
**功能描述**: 精确的时间测量和性能指标计算
- 高精度时间戳收集
- 带宽和延迟计算
- 统计分析功能

#### 2.3.2 测试配置管理
**功能描述**: 支持多种测试参数和模式配置
- 测试参数验证
- 默认配置管理
- 配置文件支持

#### 2.3.3 结果输出模块
**功能描述**: 格式化的测试结果输出
- 控制台友好输出
- CSV/JSON格式支持
- 统计图表生成

#### 2.3.4 带宽计算模块
**功能描述**: 实时带宽计算和统计
- 实时性能监控
- 历史数据管理
- 性能趋势分析

## 3. 技术实现细节

### 3.1 新增数据结构定义

#### 3.1.1 测试配置结构体
```c
// 在rdma_common.h中添加
struct benchmark_config {
    // 基础测试参数
    size_t min_message_size;        // 最小消息大小 (字节)
    size_t max_message_size;        // 最大消息大小 (字节)
    size_t message_size_step;       // 消息大小步长
    uint32_t num_iterations;        // 每个大小的迭代次数
    uint32_t warmup_iterations;     // 预热迭代次数
    
    // 测试模式配置
    enum test_mode {
        TEST_WRITE_ONLY,            // 仅RDMA Write
        TEST_READ_ONLY,             // 仅RDMA Read  
        TEST_SEND_RECV,             // Send/Recv模式
        TEST_BIDIRECTIONAL,         // 双向测试
        TEST_MIXED                  // 混合模式
    } mode;
    
    // CUDA配置
    uint32_t num_cuda_blocks;       // CUDA块数
    uint32_t threads_per_block;     // 每块线程数
    
    // 输出配置
    bool verbose_output;            // 详细输出
    bool output_csv;                // CSV格式输出
    bool output_json;               // JSON格式输出
    char output_file[256];          // 输出文件路径
    
    // 高级选项
    bool use_gpu_timing;            // 使用GPU时间戳
    bool measure_cpu_usage;         // 测量CPU使用率
    uint32_t cpu_affinity;          // CPU亲和性设置
};
```

#### 3.1.2 性能指标结构体
```c
// 新建文件: benchmark_metrics.h
struct performance_metrics {
    // 基础指标
    double bandwidth_gbps;          // 带宽 (Gbps)
    double latency_us;              // 平均延迟 (微秒)
    uint64_t total_bytes;           // 总传输字节数
    uint64_t total_operations;      // 总操作数
    double test_duration_sec;       // 测试持续时间 (秒)
    
    // 延迟统计
    double min_latency_us;          // 最小延迟
    double max_latency_us;          // 最大延迟
    double std_deviation_us;        // 标准差
    double percentile_95_us;        // 95%分位延迟
    double percentile_99_us;        // 99%分位延迟
    
    // 系统资源使用
    double cpu_utilization;         // CPU利用率 (%)
    double gpu_utilization;         // GPU利用率 (%)
    uint64_t memory_usage_mb;       // 内存使用量 (MB)
};

struct timing_sample {
    uint64_t start_timestamp;       // 开始时间戳
    uint64_t end_timestamp;         // 结束时间戳
    size_t bytes_transferred;       // 传输字节数
};
```

#### 3.1.3 扩展的RDMA配置结构体
```c
// 扩展现有的rdma_config结构体
struct rdma_config {
    // 现有字段保持不变...
    char device_name[DOCA_DEVINFO_IBDEV_NAME_SIZE];
    char gpu_pcie_addr[MAX_PCI_ADDRESS_LEN];
    char server_ip_addr[MAX_IP_ADDRESS_LEN];
    bool is_server;
    // ... 其他现有字段

    // 新增测试相关字段
    struct benchmark_config benchmark;     // 测试配置
    bool enable_benchmark;                 // 启用测试模式
    bool continuous_mode;                  // 连续测试模式
    uint32_t test_duration_sec;           // 测试持续时间
};
```

### 3.2 关键函数修改方案

#### 3.2.1 主程序入口修改
```c
// 修改gpunetio_rdma_client_server_write_main.c中的main函数
int main(int argc, char **argv)
{
    struct rdma_config cfg = {0};
    doca_error_t result;

    // 设置默认测试配置
    set_default_benchmark_config(&cfg.benchmark);

    // 现有的参数解析逻辑...
    result = doca_argp_init("doca_gpunetio_rdma_bandwidth_tool", &cfg);

    // 注册新的测试参数
    result = register_benchmark_params();

    // 验证测试配置
    result = validate_benchmark_config(&cfg.benchmark);

    // 根据配置选择执行模式
    if (cfg.enable_benchmark) {
        if (cfg.is_server) {
            result = rdma_bandwidth_test_server(&cfg);
        } else {
            result = rdma_bandwidth_test_client(&cfg);
        }
    } else {
        // 保持原有演示模式
        if (cfg.is_server) {
            result = rdma_write_server(&cfg);
        } else {
            result = rdma_write_client(&cfg);
        }
    }

    return (result == DOCA_SUCCESS) ? EXIT_SUCCESS : EXIT_FAILURE;
}
```

#### 3.2.2 新增参数注册函数
```c
// 在gpunetio_rdma_client_server_write_main.c中添加
static doca_error_t register_benchmark_params(void)
{
    doca_error_t result;
    struct doca_argp_param *param;

    // 消息大小范围参数
    result = doca_argp_param_create(&param);
    doca_argp_param_set_short_name(param, "s");
    doca_argp_param_set_long_name(param, "message-size");
    doca_argp_param_set_arguments(param, "<min:max:step>");
    doca_argp_param_set_description(param, "Message size range (bytes)");
    doca_argp_param_set_callback(param, message_size_callback);
    doca_argp_param_set_type(param, DOCA_ARGP_TYPE_STRING);
    result = doca_argp_register_param(param);

    // 迭代次数参数
    result = doca_argp_param_create(&param);
    doca_argp_param_set_short_name(param, "i");
    doca_argp_param_set_long_name(param, "iterations");
    doca_argp_param_set_arguments(param, "<number>");
    doca_argp_param_set_description(param, "Number of iterations per test");
    doca_argp_param_set_callback(param, iterations_callback);
    doca_argp_param_set_type(param, DOCA_ARGP_TYPE_INT);
    result = doca_argp_register_param(param);

    // 测试模式参数
    result = doca_argp_param_create(&param);
    doca_argp_param_set_short_name(param, "m");
    doca_argp_param_set_long_name(param, "test-mode");
    doca_argp_param_set_arguments(param, "<write|read|send|bidir|mixed>");
    doca_argp_param_set_description(param, "Test mode selection");
    doca_argp_param_set_callback(param, test_mode_callback);
    doca_argp_param_set_type(param, DOCA_ARGP_TYPE_STRING);
    result = doca_argp_register_param(param);

    // 输出格式参数
    result = doca_argp_param_create(&param);
    doca_argp_param_set_short_name(param, "o");
    doca_argp_param_set_long_name(param, "output");
    doca_argp_param_set_arguments(param, "<console|csv|json>");
    doca_argp_param_set_description(param, "Output format");
    doca_argp_param_set_callback(param, output_format_callback);
    doca_argp_param_set_type(param, DOCA_ARGP_TYPE_STRING);
    result = doca_argp_register_param(param);

    return DOCA_SUCCESS;
}
```

#### 3.2.3 性能测量核心函数
```c
// 新建文件: benchmark_utils.c
#include <time.h>
#include <sys/time.h>
#include "benchmark_metrics.h"

// 高精度时间获取
static inline uint64_t get_timestamp_ns(void)
{
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (uint64_t)ts.tv_sec * 1000000000ULL + ts.tv_nsec;
}

// 带宽计算
double calculate_bandwidth_gbps(uint64_t bytes, uint64_t time_ns)
{
    if (time_ns == 0) return 0.0;

    // 带宽 = 字节数 * 8 / 时间(秒) / 10^9
    double bits = (double)bytes * 8.0;
    double time_sec = (double)time_ns / 1000000000.0;
    return bits / time_sec / 1000000000.0;
}

// 延迟统计计算
void calculate_latency_stats(struct timing_sample *samples,
                           size_t count,
                           struct performance_metrics *metrics)
{
    if (count == 0) return;

    double *latencies = malloc(count * sizeof(double));
    double sum = 0.0;

    // 计算每个样本的延迟
    for (size_t i = 0; i < count; i++) {
        latencies[i] = (double)(samples[i].end_timestamp -
                               samples[i].start_timestamp) / 1000.0; // 转换为微秒
        sum += latencies[i];
    }

    // 排序用于计算分位数
    qsort(latencies, count, sizeof(double), compare_double);

    // 基础统计
    metrics->latency_us = sum / count;
    metrics->min_latency_us = latencies[0];
    metrics->max_latency_us = latencies[count - 1];

    // 计算标准差
    double variance = 0.0;
    for (size_t i = 0; i < count; i++) {
        double diff = latencies[i] - metrics->latency_us;
        variance += diff * diff;
    }
    metrics->std_deviation_us = sqrt(variance / count);

    // 计算分位数
    metrics->percentile_95_us = latencies[(size_t)(count * 0.95)];
    metrics->percentile_99_us = latencies[(size_t)(count * 0.99)];

    free(latencies);
}

// 性能测试主循环
doca_error_t run_bandwidth_test(struct rdma_resources *resources,
                               struct benchmark_config *config,
                               struct performance_metrics *results)
{
    doca_error_t result = DOCA_SUCCESS;
    struct timing_sample *samples;
    size_t total_samples = 0;
    uint64_t total_bytes = 0;
    uint64_t test_start_time, test_end_time;

    // 分配样本存储空间
    size_t max_samples = config->num_iterations *
                        ((config->max_message_size - config->min_message_size) /
                         config->message_size_step + 1);
    samples = malloc(max_samples * sizeof(struct timing_sample));

    test_start_time = get_timestamp_ns();

    // 遍历不同消息大小
    for (size_t msg_size = config->min_message_size;
         msg_size <= config->max_message_size;
         msg_size += config->message_size_step) {

        // 预热阶段
        for (uint32_t i = 0; i < config->warmup_iterations; i++) {
            result = execute_rdma_operation(resources, msg_size, config->mode, NULL);
            if (result != DOCA_SUCCESS) goto cleanup;
        }

        // 正式测试阶段
        for (uint32_t i = 0; i < config->num_iterations; i++) {
            struct timing_sample *sample = &samples[total_samples];

            sample->start_timestamp = get_timestamp_ns();
            result = execute_rdma_operation(resources, msg_size, config->mode, sample);
            sample->end_timestamp = get_timestamp_ns();
            sample->bytes_transferred = msg_size;

            if (result != DOCA_SUCCESS) goto cleanup;

            total_bytes += msg_size;
            total_samples++;
        }
    }

    test_end_time = get_timestamp_ns();

    // 计算最终结果
    results->total_bytes = total_bytes;
    results->total_operations = total_samples;
    results->test_duration_sec = (double)(test_end_time - test_start_time) / 1000000000.0;
    results->bandwidth_gbps = calculate_bandwidth_gbps(total_bytes,
                                                      test_end_time - test_start_time);

    // 计算延迟统计
    calculate_latency_stats(samples, total_samples, results);

cleanup:
    free(samples);
    return result;
}
```

### 3.3 CUDA内核重写要点

#### 3.3.1 带宽测试内核设计
```cuda
// 修改device/gpunetio_rdma_client_server_write_kernel.cu

// 新的带宽测试内核
__global__ void bandwidth_test_kernel(
    struct doca_gpu_dev_rdma *rdma_gpu,
    struct doca_gpu_buf_arr *local_buf_arr,
    struct doca_gpu_buf_arr *remote_buf_arr,
    size_t message_size,
    uint32_t num_iterations,
    enum test_mode mode,
    uint32_t connection_index,
    uint64_t *timing_results,
    uint32_t *completion_counter)
{
    int tid = threadIdx.x + blockIdx.x * blockDim.x;
    int total_threads = blockDim.x * gridDim.x;

    // 每个线程处理的迭代数
    uint32_t iterations_per_thread = (num_iterations + total_threads - 1) / total_threads;
    uint32_t start_iter = tid * iterations_per_thread;
    uint32_t end_iter = min(start_iter + iterations_per_thread, num_iterations);

    struct doca_gpu_buf *local_buf, *remote_buf;
    doca_error_t result;

    // 获取缓冲区
    result = doca_gpu_dev_buf_get_buf(local_buf_arr, tid % GPU_BUF_NUM, &local_buf);
    if (result != DOCA_SUCCESS) return;

    result = doca_gpu_dev_buf_get_buf(remote_buf_arr, tid % GPU_BUF_NUM, &remote_buf);
    if (result != DOCA_SUCCESS) return;

    // 执行测试循环
    for (uint32_t iter = start_iter; iter < end_iter; iter++) {
        uint64_t start_time = clock64();

        // 根据测试模式执行不同的RDMA操作
        switch (mode) {
            case TEST_WRITE_ONLY:
                result = doca_gpu_dev_rdma_write_strong(
                    rdma_gpu, connection_index,
                    remote_buf, 0,
                    local_buf, 0,
                    message_size, 0,
                    DOCA_GPU_RDMA_WRITE_FLAG_NONE);
                break;

            case TEST_READ_ONLY:
                result = doca_gpu_dev_rdma_read_strong(
                    rdma_gpu, connection_index,
                    local_buf, 0,
                    remote_buf, 0,
                    message_size, 0);
                break;

            case TEST_SEND_RECV:
                result = doca_gpu_dev_rdma_send_strong(
                    rdma_gpu, connection_index,
                    local_buf, 0,
                    message_size, 0,
                    DOCA_GPU_RDMA_SEND_FLAG_NONE);
                break;

            default:
                return;
        }

        if (result == DOCA_SUCCESS) {
            // 提交操作
            result = doca_gpu_dev_rdma_commit_strong(rdma_gpu, connection_index);

            if (result == DOCA_SUCCESS) {
                uint64_t end_time = clock64();

                // 存储时间测量结果
                if (timing_results && iter < num_iterations) {
                    timing_results[iter * 2] = start_time;
                    timing_results[iter * 2 + 1] = end_time;
                }
            }
        }
    }

    // 原子递增完成计数器
    atomicAdd(completion_counter, end_iter - start_iter);
}

// 双向测试内核
__global__ void bidirectional_test_kernel(
    struct doca_gpu_dev_rdma *rdma_gpu,
    struct doca_gpu_buf_arr *local_buf_arr,
    struct doca_gpu_buf_arr *remote_buf_arr,
    size_t message_size,
    uint32_t num_iterations,
    uint32_t connection_index,
    uint64_t *timing_results,
    uint32_t *completion_counter)
{
    int tid = threadIdx.x + blockIdx.x * blockDim.x;

    // 一半线程执行写操作，一半执行读操作
    bool is_writer = (tid % 2 == 0);

    struct doca_gpu_buf *local_buf, *remote_buf;
    doca_error_t result;

    result = doca_gpu_dev_buf_get_buf(local_buf_arr, tid % GPU_BUF_NUM, &local_buf);
    if (result != DOCA_SUCCESS) return;

    result = doca_gpu_dev_buf_get_buf(remote_buf_arr, tid % GPU_BUF_NUM, &remote_buf);
    if (result != DOCA_SUCCESS) return;

    for (uint32_t iter = 0; iter < num_iterations; iter++) {
        uint64_t start_time = clock64();

        if (is_writer) {
            // 执行写操作
            result = doca_gpu_dev_rdma_write_strong(
                rdma_gpu, connection_index,
                remote_buf, 0,
                local_buf, 0,
                message_size, 0,
                DOCA_GPU_RDMA_WRITE_FLAG_NONE);
        } else {
            // 执行读操作
            result = doca_gpu_dev_rdma_read_strong(
                rdma_gpu, connection_index,
                local_buf, 0,
                remote_buf, 0,
                message_size, 0);
        }

        if (result == DOCA_SUCCESS) {
            result = doca_gpu_dev_rdma_commit_strong(rdma_gpu, connection_index);

            if (result == DOCA_SUCCESS) {
                uint64_t end_time = clock64();

                // 存储时间测量结果
                size_t result_index = (tid * num_iterations + iter) * 2;
                if (timing_results) {
                    timing_results[result_index] = start_time;
                    timing_results[result_index + 1] = end_time;
                }
            }
        }

        // 同步确保读写操作交替进行
        __syncthreads();
    }

    atomicAdd(completion_counter, num_iterations);
}
```

#### 3.3.2 内核启动包装函数
```c
// 在device/gpunetio_rdma_client_server_write_kernel.cu中添加

extern "C" {

doca_error_t launch_bandwidth_test_kernel(
    cudaStream_t stream,
    struct doca_gpu_dev_rdma *rdma_gpu,
    struct doca_gpu_buf_arr *local_buf_arr,
    struct doca_gpu_buf_arr *remote_buf_arr,
    struct benchmark_config *config,
    uint32_t connection_index,
    uint64_t *gpu_timing_results,
    uint32_t *gpu_completion_counter)
{
    cudaError_t cuda_result;

    // 验证输入参数
    if (!rdma_gpu || !local_buf_arr || !remote_buf_arr || !config) {
        return DOCA_ERROR_INVALID_VALUE;
    }

    // 检查CUDA错误状态
    cuda_result = cudaGetLastError();
    if (cuda_result != cudaSuccess) {
        DOCA_LOG_ERR("CUDA error before kernel launch: %s",
                     cudaGetErrorString(cuda_result));
        return DOCA_ERROR_BAD_STATE;
    }

    // 根据测试模式选择内核
    if (config->mode == TEST_BIDIRECTIONAL) {
        bidirectional_test_kernel<<<config->num_cuda_blocks,
                                   config->threads_per_block,
                                   0, stream>>>(
            rdma_gpu, local_buf_arr, remote_buf_arr,
            config->max_message_size, config->num_iterations,
            connection_index, gpu_timing_results, gpu_completion_counter);
    } else {
        bandwidth_test_kernel<<<config->num_cuda_blocks,
                               config->threads_per_block,
                               0, stream>>>(
            rdma_gpu, local_buf_arr, remote_buf_arr,
            config->max_message_size, config->num_iterations,
            config->mode, connection_index,
            gpu_timing_results, gpu_completion_counter);
    }

    // 检查内核启动错误
    cuda_result = cudaGetLastError();
    if (cuda_result != cudaSuccess) {
        DOCA_LOG_ERR("CUDA kernel launch failed: %s",
                     cudaGetErrorString(cuda_result));
        return DOCA_ERROR_BAD_STATE;
    }

    return DOCA_SUCCESS;
}

} // extern "C"
```

### 3.4 结果输出模块实现

#### 3.4.1 控制台输出格式
```c
// 新建文件: output_formatter.c

void print_test_results_console(struct performance_metrics *metrics,
                               struct benchmark_config *config)
{
    printf("\n=== GPU RDMA Bandwidth Test Results ===\n");
    printf("Test Configuration:\n");
    printf("  Message Size Range: %zu - %zu bytes (step: %zu)\n",
           config->min_message_size, config->max_message_size,
           config->message_size_step);
    printf("  Iterations per size: %u\n", config->num_iterations);
    printf("  Test Mode: %s\n", test_mode_to_string(config->mode));
    printf("  CUDA Blocks: %u, Threads per Block: %u\n",
           config->num_cuda_blocks, config->threads_per_block);

    printf("\nPerformance Results:\n");
    printf("  Total Bandwidth: %.2f Gbps\n", metrics->bandwidth_gbps);
    printf("  Average Latency: %.2f μs\n", metrics->latency_us);
    printf("  Total Data Transferred: %.2f MB\n",
           (double)metrics->total_bytes / (1024 * 1024));
    printf("  Total Operations: %lu\n", metrics->total_operations);
    printf("  Test Duration: %.2f seconds\n", metrics->test_duration_sec);

    printf("\nLatency Statistics:\n");
    printf("  Min Latency: %.2f μs\n", metrics->min_latency_us);
    printf("  Max Latency: %.2f μs\n", metrics->max_latency_us);
    printf("  Std Deviation: %.2f μs\n", metrics->std_deviation_us);
    printf("  95th Percentile: %.2f μs\n", metrics->percentile_95_us);
    printf("  99th Percentile: %.2f μs\n", metrics->percentile_99_us);

    if (config->measure_cpu_usage) {
        printf("\nSystem Resource Usage:\n");
        printf("  CPU Utilization: %.1f%%\n", metrics->cpu_utilization);
        printf("  GPU Utilization: %.1f%%\n", metrics->gpu_utilization);
        printf("  Memory Usage: %lu MB\n", metrics->memory_usage_mb);
    }

    printf("=====================================\n\n");
}
```

#### 3.4.2 CSV输出格式
```c
void export_results_csv(struct performance_metrics *metrics,
                       struct benchmark_config *config,
                       const char *filename)
{
    FILE *fp = fopen(filename, "w");
    if (!fp) {
        DOCA_LOG_ERR("Failed to open CSV file: %s", filename);
        return;
    }

    // CSV头部
    fprintf(fp, "timestamp,test_mode,min_msg_size,max_msg_size,iterations,"
               "bandwidth_gbps,avg_latency_us,min_latency_us,max_latency_us,"
               "std_deviation_us,p95_latency_us,p99_latency_us,"
               "total_bytes,total_operations,test_duration_sec,"
               "cpu_utilization,gpu_utilization,memory_usage_mb\n");

    // 获取当前时间戳
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    char timestamp[64];
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);

    // 输出数据行
    fprintf(fp, "%s,%s,%zu,%zu,%u,"
               "%.2f,%.2f,%.2f,%.2f,"
               "%.2f,%.2f,%.2f,"
               "%lu,%lu,%.2f,"
               "%.1f,%.1f,%lu\n",
            timestamp, test_mode_to_string(config->mode),
            config->min_message_size, config->max_message_size, config->num_iterations,
            metrics->bandwidth_gbps, metrics->latency_us,
            metrics->min_latency_us, metrics->max_latency_us,
            metrics->std_deviation_us, metrics->percentile_95_us, metrics->percentile_99_us,
            metrics->total_bytes, metrics->total_operations, metrics->test_duration_sec,
            metrics->cpu_utilization, metrics->gpu_utilization, metrics->memory_usage_mb);

    fclose(fp);
    printf("Results exported to: %s\n", filename);
}
```

## 4. 集成和测试建议

### 4.1 渐进式集成策略
1. **第一阶段**: 扩展参数解析，添加基础测试配置
2. **第二阶段**: 实现简单的带宽测试内核
3. **第三阶段**: 添加性能测量和统计功能
4. **第四阶段**: 完善输出格式和高级功能

### 4.2 验证方法
- 与现有RDMA性能工具(如ib_send_bw)对比验证
- 使用已知网络配置进行基准测试
- 多次运行确保结果一致性

### 4.3 调试建议
- 使用CUDA调试工具验证内核执行
- 添加详细的日志输出用于问题诊断
- 实现渐进式功能启用，便于定位问题

这份技术指南提供了将GPU RDMA示例程序改造为专业带宽测量工具的完整实现方案，包含了所有必要的代码结构、函数定义和实现细节。
```
