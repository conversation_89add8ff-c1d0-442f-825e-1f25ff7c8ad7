#!/bin/bash

# RDMA Write Bandwidth Test Script
# This script demonstrates how to run the RDMA write bandwidth test

# Configuration
DEVICE_NAME="mlx5_0"  # Change this to your IB device name
GPU_PCI_ADDR="0000:01:00.0"  # Change this to your GPU PCI address
SERVER_IP="*************"  # Change this to your server IP

# Test parameters
MESSAGE_SIZES=(1024 4096 16384 65536 262144 1048576)  # Different message sizes to test
ITERATIONS=1000
WARMUP_ITERATIONS=100

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if required parameters are set
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if executable exists
    if [ ! -f "./build/doca_gpunetio_rdma_client_server_write" ]; then
        print_error "Executable not found. Please build the project first:"
        echo "  ./build.sh"
        exit 1
    fi
    
    # Check if device exists
    if [ ! -d "/sys/class/infiniband/$DEVICE_NAME" ]; then
        print_warning "InfiniBand device $DEVICE_NAME not found. Please update DEVICE_NAME in this script."
    fi
    
    print_info "Prerequisites check completed"
}

# Function to run server
run_server() {
    local message_size=$1
    local log_file="server_${message_size}.log"
    
    print_info "Starting server for message size $message_size bytes..."
    
    ./build/doca_gpunetio_rdma_client_server_write \
        --device $DEVICE_NAME \
        --gpu $GPU_PCI_ADDR \
        --write-bw-test \
        --message-size $message_size \
        --iterations $ITERATIONS \
        --warmup $WARMUP_ITERATIONS \
        --verbose > $log_file 2>&1 &
    
    local server_pid=$!
    echo $server_pid > server.pid
    
    print_info "Server started with PID $server_pid, log: $log_file"
    sleep 2  # Give server time to start
}

# Function to run client
run_client() {
    local message_size=$1
    local log_file="client_${message_size}.log"
    
    print_info "Starting client for message size $message_size bytes..."
    
    ./build/doca_gpunetio_rdma_client_server_write \
        --device $DEVICE_NAME \
        --gpu $GPU_PCI_ADDR \
        --client $SERVER_IP \
        --write-bw-test \
        --message-size $message_size \
        --iterations $ITERATIONS \
        --warmup $WARMUP_ITERATIONS \
        --verbose > $log_file 2>&1
    
    local client_result=$?
    print_info "Client completed with exit code $client_result, log: $log_file"
    
    return $client_result
}

# Function to stop server
stop_server() {
    if [ -f server.pid ]; then
        local server_pid=$(cat server.pid)
        print_info "Stopping server (PID: $server_pid)..."
        kill $server_pid 2>/dev/null
        wait $server_pid 2>/dev/null
        rm -f server.pid
    fi
}

# Function to run bandwidth test for a specific message size
run_bandwidth_test() {
    local message_size=$1
    
    print_info "Running bandwidth test for message size: $message_size bytes"
    print_info "Iterations: $ITERATIONS, Warmup: $WARMUP_ITERATIONS"
    
    # Start server
    run_server $message_size
    
    # Run client
    if run_client $message_size; then
        print_info "Test completed successfully for message size $message_size"
        
        # Extract and display results
        if [ -f "client_${message_size}.log" ]; then
            echo "Results for $message_size bytes:"
            grep -E "(Bandwidth|Latency|Total)" "client_${message_size}.log" | sed 's/^/  /'
            echo
        fi
    else
        print_error "Test failed for message size $message_size"
    fi
    
    # Stop server
    stop_server
    
    sleep 1  # Brief pause between tests
}

# Function to run comprehensive bandwidth test
run_comprehensive_test() {
    print_info "Starting comprehensive RDMA write bandwidth test"
    print_info "Testing message sizes: ${MESSAGE_SIZES[*]}"
    
    # Create results directory
    mkdir -p results
    cd results
    
    # Run tests for each message size
    for size in "${MESSAGE_SIZES[@]}"; do
        run_bandwidth_test $size
    done
    
    print_info "All tests completed. Results are in the 'results' directory."
    
    # Generate summary
    print_info "Generating summary..."
    echo "RDMA Write Bandwidth Test Summary" > summary.txt
    echo "=================================" >> summary.txt
    echo "Test Date: $(date)" >> summary.txt
    echo "Device: $DEVICE_NAME" >> summary.txt
    echo "GPU: $GPU_PCI_ADDR" >> summary.txt
    echo "Iterations: $ITERATIONS" >> summary.txt
    echo "Warmup: $WARMUP_ITERATIONS" >> summary.txt
    echo "" >> summary.txt
    
    for size in "${MESSAGE_SIZES[@]}"; do
        if [ -f "client_${size}.log" ]; then
            echo "Message Size: $size bytes" >> summary.txt
            grep "Bandwidth:" "client_${size}.log" | head -1 >> summary.txt
            echo "" >> summary.txt
        fi
    done
    
    print_info "Summary saved to results/summary.txt"
    cd ..
}

# Function to run single test
run_single_test() {
    local message_size=${1:-4096}
    
    print_info "Running single bandwidth test"
    print_info "Message size: $message_size bytes"
    
    mkdir -p results
    cd results
    
    run_bandwidth_test $message_size
    
    cd ..
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -s, --single [SIZE]     Run single test with specified message size (default: 4096)"
    echo "  -c, --comprehensive     Run comprehensive test with multiple message sizes"
    echo "  -d, --device DEVICE     Set InfiniBand device name (default: $DEVICE_NAME)"
    echo "  -g, --gpu GPU_ADDR      Set GPU PCI address (default: $GPU_PCI_ADDR)"
    echo "  -i, --ip SERVER_IP      Set server IP address (default: $SERVER_IP)"
    echo "  --iterations COUNT      Set number of iterations (default: $ITERATIONS)"
    echo "  --warmup COUNT          Set number of warmup iterations (default: $WARMUP_ITERATIONS)"
    echo ""
    echo "Examples:"
    echo "  $0 --single 1024        # Run single test with 1024 byte messages"
    echo "  $0 --comprehensive      # Run comprehensive test with multiple sizes"
    echo "  $0 -d mlx5_1 -g 0000:02:00.0 --single  # Use different device and GPU"
}

# Cleanup function
cleanup() {
    print_info "Cleaning up..."
    stop_server
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main script logic
main() {
    local mode="single"
    local message_size=4096
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -s|--single)
                mode="single"
                if [[ $2 =~ ^[0-9]+$ ]]; then
                    message_size=$2
                    shift
                fi
                shift
                ;;
            -c|--comprehensive)
                mode="comprehensive"
                shift
                ;;
            -d|--device)
                DEVICE_NAME="$2"
                shift 2
                ;;
            -g|--gpu)
                GPU_PCI_ADDR="$2"
                shift 2
                ;;
            -i|--ip)
                SERVER_IP="$2"
                shift 2
                ;;
            --iterations)
                ITERATIONS="$2"
                shift 2
                ;;
            --warmup)
                WARMUP_ITERATIONS="$2"
                shift 2
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Check prerequisites
    check_prerequisites
    
    # Run the appropriate test
    case $mode in
        single)
            run_single_test $message_size
            ;;
        comprehensive)
            run_comprehensive_test
            ;;
        *)
            print_error "Invalid mode: $mode"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
