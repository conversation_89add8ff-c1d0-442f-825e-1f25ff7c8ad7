/*
 * Copyright (c) 2024 NVIDIA CORPORATION AND AFFILIATES.  All rights reserved.
 *
 * RDMA Write Bandwidth Test Implementation
 * This file implements the RDMA write bandwidth testing functionality
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <time.h>
#include <sys/time.h>
#include <math.h>
#include <float.h>
#include <sys/socket.h>

#include <cuda_runtime.h>

#include <doca_log.h>
#include <doca_error.h>

#include "rdma_common.h"
#include "common.h"

DOCA_LOG_REGISTER(RDMA_WRITE_BW_TEST);

/* Default configuration values */
#define DEFAULT_MESSAGE_SIZE 4096
#define DEFAULT_NUM_ITERATIONS 1000
#define DEFAULT_WARMUP_ITERATIONS 100
#define DEFAULT_NUM_CUDA_BLOCKS 1
#define DEFAULT_THREADS_PER_BLOCK 32

/*
 * Set default write bandwidth test configuration
 */
static void set_default_write_bw_config(struct write_bw_config *config)
{
	config->message_size = DEFAULT_MESSAGE_SIZE;
	config->num_iterations = DEFAULT_NUM_ITERATIONS;
	config->warmup_iterations = DEFAULT_WARMUP_ITERATIONS;
	config->num_cuda_blocks = DEFAULT_NUM_CUDA_BLOCKS;
	config->threads_per_block = DEFAULT_THREADS_PER_BLOCK;
	config->verbose_output = false;
	config->measure_latency = true;
}

/*
 * Get high precision timestamp in nanoseconds
 */
static inline uint64_t get_timestamp_ns(void)
{
	struct timespec ts;
	clock_gettime(CLOCK_MONOTONIC, &ts);
	return (uint64_t)ts.tv_sec * 1000000000ULL + ts.tv_nsec;
}

/*
 * Calculate bandwidth in Gbps
 */
static double calculate_bandwidth_gbps(uint64_t bytes, uint64_t time_ns)
{
	if (time_ns == 0) return 0.0;
	
	double bits = (double)bytes * 8.0;
	double time_sec = (double)time_ns / 1000000000.0;
	return bits / time_sec / 1000000000.0;
}

/*
 * Print bandwidth test results
 */
static void print_write_bw_results(struct write_bw_config *config, 
				   struct write_bw_metrics *metrics)
{
	printf("\n=== RDMA Write Bandwidth Test Results ===\n");
	printf("Configuration:\n");
	printf("  Message Size: %zu bytes\n", config->message_size);
	printf("  Iterations: %u\n", config->num_iterations);
	printf("  Warmup Iterations: %u\n", config->warmup_iterations);
	printf("  CUDA Blocks: %u\n", config->num_cuda_blocks);
	printf("  Threads per Block: %u\n", config->threads_per_block);
	
	printf("\nResults:\n");
	printf("  Bandwidth: %.2f Gbps\n", metrics->bandwidth_gbps);
	printf("  Total Data: %.2f MB\n", (double)metrics->total_bytes / (1024 * 1024));
	printf("  Total Operations: %lu\n", metrics->total_operations);
	printf("  Test Duration: %.3f seconds\n", metrics->test_duration_sec);
	
	if (config->measure_latency) {
		printf("  Average Latency: %.2f μs\n", metrics->avg_latency_us);
		printf("  Min Latency: %.2f μs\n", metrics->min_latency_us);
		printf("  Max Latency: %.2f μs\n", metrics->max_latency_us);
	}
	
	printf("==========================================\n\n");
}

/*
 * Allocate GPU memory for timing results and completion counter
 */
static doca_error_t allocate_test_buffers(struct rdma_resources *resources,
					  struct write_bw_config *config,
					  uint64_t **gpu_timing_results,
					  uint64_t **cpu_timing_results,
					  uint32_t **gpu_completion_counter,
					  uint32_t **cpu_completion_counter)
{
	doca_error_t result;
	size_t timing_buffer_size = config->num_iterations * 2 * sizeof(uint64_t);
	
	/* Allocate timing results buffer */
	result = doca_gpu_mem_alloc(resources->gpudev,
				    timing_buffer_size,
				    4096,
				    DOCA_GPU_MEM_TYPE_GPU_CPU,
				    (void **)gpu_timing_results,
				    (void **)cpu_timing_results);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to allocate timing results buffer: %s", 
			     doca_error_get_descr(result));
		return result;
	}
	
	/* Allocate completion counter */
	result = doca_gpu_mem_alloc(resources->gpudev,
				    sizeof(uint32_t),
				    4096,
				    DOCA_GPU_MEM_TYPE_GPU_CPU,
				    (void **)gpu_completion_counter,
				    (void **)cpu_completion_counter);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to allocate completion counter: %s", 
			     doca_error_get_descr(result));
		doca_gpu_mem_free(resources->gpudev, *gpu_timing_results);
		return result;
	}
	
	/* Initialize completion counter */
	**cpu_completion_counter = 0;
	
	return DOCA_SUCCESS;
}

/*
 * Free test buffers
 */
static void free_test_buffers(struct rdma_resources *resources,
			     uint64_t *gpu_timing_results,
			     uint32_t *gpu_completion_counter)
{
	if (gpu_timing_results) {
		doca_gpu_mem_free(resources->gpudev, gpu_timing_results);
	}
	if (gpu_completion_counter) {
		doca_gpu_mem_free(resources->gpudev, gpu_completion_counter);
	}
}

/*
 * Calculate latency statistics from timing results
 */
static void calculate_latency_stats(uint64_t *timing_results, 
				   uint32_t num_samples,
				   struct write_bw_metrics *metrics)
{
	if (num_samples == 0) return;
	
	double sum_latency = 0.0;
	double min_latency = DBL_MAX;
	double max_latency = 0.0;
	
	for (uint32_t i = 0; i < num_samples; i++) {
		uint64_t start_time = timing_results[i * 2];
		uint64_t end_time = timing_results[i * 2 + 1];
		
		if (end_time > start_time) {
			/* Convert GPU clock cycles to microseconds (approximate) */
			double latency_us = (double)(end_time - start_time) / 1000.0;
			
			sum_latency += latency_us;
			if (latency_us < min_latency) min_latency = latency_us;
			if (latency_us > max_latency) max_latency = latency_us;
		}
	}
	
	metrics->avg_latency_us = sum_latency / num_samples;
	metrics->min_latency_us = min_latency;
	metrics->max_latency_us = max_latency;
}

/*
 * Run the actual bandwidth test
 */
static doca_error_t run_write_bw_test(struct rdma_resources *resources,
				      struct buf_arr_obj *local_buf_arr,
				      struct buf_arr_obj *remote_buf_arr,
				      struct write_bw_config *config,
				      struct write_bw_metrics *metrics)
{
	doca_error_t result;
	cudaError_t cuda_result;
	cudaStream_t stream;
	uint64_t *gpu_timing_results = NULL;
	uint64_t *cpu_timing_results = NULL;
	uint32_t *gpu_completion_counter = NULL;
	uint32_t *cpu_completion_counter = NULL;
	uint64_t test_start_time, test_end_time;
	
	/* Create CUDA stream */
	cuda_result = cudaStreamCreateWithFlags(&stream, cudaStreamNonBlocking);
	if (cuda_result != cudaSuccess) {
		DOCA_LOG_ERR("Failed to create CUDA stream: %s", cudaGetErrorString(cuda_result));
		return DOCA_ERROR_DRIVER;
	}
	
	/* Allocate test buffers */
	result = allocate_test_buffers(resources, config,
				      &gpu_timing_results, &cpu_timing_results,
				      &gpu_completion_counter, &cpu_completion_counter);
	if (result != DOCA_SUCCESS) {
		goto cleanup_stream;
	}
	
	DOCA_LOG_INFO("Starting RDMA write bandwidth test...");
	DOCA_LOG_INFO("Message size: %zu bytes, Iterations: %u", 
		      config->message_size, config->num_iterations);
	
	/* Warmup phase */
	if (config->warmup_iterations > 0) {
		DOCA_LOG_INFO("Running %u warmup iterations...", config->warmup_iterations);
		
		struct write_bw_config warmup_config = *config;
		warmup_config.num_iterations = config->warmup_iterations;
		
		result = launch_write_bw_test_kernel(stream, resources->gpu_rdma,
						    local_buf_arr->gpu_buf_arr,
						    remote_buf_arr->gpu_buf_arr,
						    &warmup_config, 0,
						    NULL, gpu_completion_counter);
		if (result != DOCA_SUCCESS) {
			DOCA_LOG_ERR("Warmup test failed: %s", doca_error_get_descr(result));
			goto cleanup_buffers;
		}
		
		/* Wait for warmup to complete */
		cuda_result = cudaStreamSynchronize(stream);
		if (cuda_result != cudaSuccess) {
			DOCA_LOG_ERR("CUDA stream sync failed during warmup: %s", 
				     cudaGetErrorString(cuda_result));
			result = DOCA_ERROR_DRIVER;
			goto cleanup_buffers;
		}
		
		/* Reset completion counter */
		*cpu_completion_counter = 0;
	}
	
	/* Main test phase */
	DOCA_LOG_INFO("Running main test with %u iterations...", config->num_iterations);
	
	test_start_time = get_timestamp_ns();
	
	result = launch_write_bw_test_kernel(stream, resources->gpu_rdma,
					    local_buf_arr->gpu_buf_arr,
					    remote_buf_arr->gpu_buf_arr,
					    config, 0,
					    gpu_timing_results, gpu_completion_counter);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Main test failed: %s", doca_error_get_descr(result));
		goto cleanup_buffers;
	}
	
	/* Wait for test completion */
	cuda_result = cudaStreamSynchronize(stream);
	if (cuda_result != cudaSuccess) {
		DOCA_LOG_ERR("CUDA stream sync failed: %s", cudaGetErrorString(cuda_result));
		result = DOCA_ERROR_DRIVER;
		goto cleanup_buffers;
	}
	
	test_end_time = get_timestamp_ns();
	
	/* Calculate results */
	metrics->total_operations = *cpu_completion_counter;
	metrics->total_bytes = metrics->total_operations * config->message_size;
	metrics->test_duration_sec = (double)(test_end_time - test_start_time) / 1000000000.0;
	metrics->bandwidth_gbps = calculate_bandwidth_gbps(metrics->total_bytes, 
							   test_end_time - test_start_time);
	
	/* Calculate latency statistics if enabled */
	if (config->measure_latency && cpu_timing_results) {
		uint32_t num_samples = (config->num_iterations < metrics->total_operations) ?
					config->num_iterations : metrics->total_operations;
		calculate_latency_stats(cpu_timing_results, num_samples, metrics);
	}
	
	DOCA_LOG_INFO("Test completed successfully");
	
cleanup_buffers:
	free_test_buffers(resources, gpu_timing_results, gpu_completion_counter);
	
cleanup_stream:
	cudaStreamDestroy(stream);
	
	return result;
}

/*
 * Create memory mappings for bandwidth test
 */
static doca_error_t create_write_bw_memory(struct rdma_resources *resources,
					   struct write_bw_config *config,
					   struct rdma_mmap_obj *local_mmap,
					   struct buf_arr_obj *local_buf_arr,
					   bool is_server)
{
	doca_error_t result;
	cudaError_t cuda_result;
	size_t buffer_size;
	void *gpu_buffer = NULL;
	void *cpu_buffer = NULL;

	/* Calculate buffer size based on message size and number of buffers */
	buffer_size = MAX(config->message_size * GPU_BUF_NUM, GPU_BUF_SIZE_A * GPU_BUF_NUM);
	buffer_size = ROUND_UP(buffer_size, 4096); /* Align to page size */

	/* Allocate GPU memory */
	result = doca_gpu_mem_alloc(resources->gpudev,
				    buffer_size,
				    4096,
				    DOCA_GPU_MEM_TYPE_GPU_CPU,
				    &gpu_buffer,
				    &cpu_buffer);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to allocate GPU memory: %s", doca_error_get_descr(result));
		return result;
	}

	/* Initialize buffer with test pattern */
	if (is_server) {
		memset(cpu_buffer, 0xAA, buffer_size); /* Server pattern */
	} else {
		memset(cpu_buffer, 0x55, buffer_size); /* Client pattern */
	}

	/* Set up memory mapping */
	local_mmap->doca_device = resources->doca_device;
	local_mmap->permissions = DOCA_ACCESS_FLAG_LOCAL_READ_WRITE | DOCA_ACCESS_FLAG_RDMA_WRITE;
	local_mmap->memrange_addr = gpu_buffer;
	local_mmap->memrange_len = buffer_size;

	/* Create DOCA memory mapping */
	result = create_mmap(local_mmap);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to create memory mapping: %s", doca_error_get_descr(result));
		doca_gpu_mem_free(resources->gpudev, gpu_buffer);
		return result;
	}

	/* Set up buffer array */
	local_buf_arr->gpudev = resources->gpudev;
	local_buf_arr->mmap = local_mmap->mmap;
	local_buf_arr->num_elem = GPU_BUF_NUM;
	local_buf_arr->elem_size = buffer_size / GPU_BUF_NUM;

	/* Create GPU buffer array */
	result = create_buf_arr_on_gpu(local_buf_arr);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to create buffer array: %s", doca_error_get_descr(result));
		doca_mmap_destroy(local_mmap->mmap);
		doca_gpu_mem_free(resources->gpudev, gpu_buffer);
		return result;
	}

	return DOCA_SUCCESS;
}

/*
 * Exchange memory mappings between client and server
 */
static doca_error_t exchange_memory_mappings(int sock_fd,
					     struct rdma_mmap_obj *local_mmap,
					     struct doca_mmap **remote_mmap,
					     struct rdma_resources *resources,
					     bool is_server)
{
	doca_error_t result;
	void *remote_export = NULL;
	size_t remote_export_len;

	if (is_server) {
		/* Server: send local mapping, then receive remote mapping */
		if (send(sock_fd, &local_mmap->export_len, sizeof(size_t), 0) < 0) {
			DOCA_LOG_ERR("Failed to send export length");
			return DOCA_ERROR_CONNECTION_ABORTED;
		}

		if (send(sock_fd, local_mmap->rdma_export, local_mmap->export_len, 0) < 0) {
			DOCA_LOG_ERR("Failed to send memory export");
			return DOCA_ERROR_CONNECTION_ABORTED;
		}

		if (recv(sock_fd, &remote_export_len, sizeof(size_t), 0) < 0) {
			DOCA_LOG_ERR("Failed to receive remote export length");
			return DOCA_ERROR_CONNECTION_ABORTED;
		}
	} else {
		/* Client: receive remote mapping, then send local mapping */
		if (recv(sock_fd, &remote_export_len, sizeof(size_t), 0) < 0) {
			DOCA_LOG_ERR("Failed to receive remote export length");
			return DOCA_ERROR_CONNECTION_ABORTED;
		}
	}

	/* Allocate buffer for remote export */
	remote_export = malloc(remote_export_len);
	if (!remote_export) {
		DOCA_LOG_ERR("Failed to allocate memory for remote export");
		return DOCA_ERROR_NO_MEMORY;
	}

	if (is_server) {
		if (recv(sock_fd, remote_export, remote_export_len, 0) < 0) {
			DOCA_LOG_ERR("Failed to receive remote export");
			free(remote_export);
			return DOCA_ERROR_CONNECTION_ABORTED;
		}
	} else {
		if (recv(sock_fd, remote_export, remote_export_len, 0) < 0) {
			DOCA_LOG_ERR("Failed to receive remote export");
			free(remote_export);
			return DOCA_ERROR_CONNECTION_ABORTED;
		}

		/* Send local mapping */
		if (send(sock_fd, &local_mmap->export_len, sizeof(size_t), 0) < 0) {
			DOCA_LOG_ERR("Failed to send export length");
			free(remote_export);
			return DOCA_ERROR_CONNECTION_ABORTED;
		}

		if (send(sock_fd, local_mmap->rdma_export, local_mmap->export_len, 0) < 0) {
			DOCA_LOG_ERR("Failed to send memory export");
			free(remote_export);
			return DOCA_ERROR_CONNECTION_ABORTED;
		}
	}

	/* Create remote memory mapping */
	result = doca_mmap_create_from_export(NULL, remote_export, remote_export_len,
					      resources->doca_device, remote_mmap);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to create remote memory mapping: %s",
			     doca_error_get_descr(result));
		free(remote_export);
		return result;
	}

	free(remote_export);
	return DOCA_SUCCESS;
}

/*
 * RDMA Write bandwidth test - Client side implementation
 */
doca_error_t rdma_write_bw_test_client(struct rdma_config *cfg)
{
	struct rdma_resources resources = {0};
	struct rdma_mmap_obj local_mmap = {0};
	struct buf_arr_obj local_buf_arr = {0};
	struct buf_arr_obj remote_buf_arr = {0};
	struct doca_mmap *remote_mmap = NULL;
	struct write_bw_metrics metrics = {0};
	doca_error_t result;
	int oob_sock_fd = -1;
	const uint32_t rdma_permissions = DOCA_ACCESS_FLAG_LOCAL_READ_WRITE | DOCA_ACCESS_FLAG_RDMA_WRITE;

	/* Set default configuration if not set */
	if (!cfg->enable_write_bw_test) {
		set_default_write_bw_config(&cfg->write_bw);
		cfg->enable_write_bw_test = true;
	}

	DOCA_LOG_INFO("Starting RDMA Write Bandwidth Test - Client");
	DOCA_LOG_INFO("Message size: %zu bytes, Iterations: %u",
		      cfg->write_bw.message_size, cfg->write_bw.num_iterations);

	/* Create RDMA resources */
	result = create_rdma_resources(cfg, rdma_permissions, &resources);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to create RDMA resources: %s", doca_error_get_descr(result));
		return result;
	}

	/* Get GPU RDMA handle */
	result = doca_rdma_get_gpu_handle(resources.rdma, &resources.gpu_rdma);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to get GPU RDMA handle: %s", doca_error_get_descr(result));
		goto cleanup_resources;
	}

	/* Setup OOB connection to server */
	if (oob_connection_client_setup(cfg->server_ip_addr, &oob_sock_fd) < 0) {
		DOCA_LOG_ERR("Failed to setup OOB connection");
		result = DOCA_ERROR_CONNECTION_ABORTED;
		goto cleanup_resources;
	}

	/* Establish RDMA connection */
	if (!cfg->use_rdma_cm) {
		/* Use OOB connection method */
		void *remote_conn_details = NULL;
		size_t remote_conn_details_len = 0;
		struct doca_rdma_connection *connection = NULL;

		/* Export local connection details */
		result = doca_rdma_export(resources.rdma, &resources.connection_details,
					  &resources.conn_det_len, &connection);
		if (result != DOCA_SUCCESS) {
			DOCA_LOG_ERR("Failed to export RDMA connection: %s", doca_error_get_descr(result));
			goto cleanup_connection;
		}

		/* Receive server connection details */
		if (recv(oob_sock_fd, &remote_conn_details_len, sizeof(size_t), 0) < 0) {
			DOCA_LOG_ERR("Failed to receive connection details length");
			result = DOCA_ERROR_CONNECTION_ABORTED;
			goto cleanup_connection;
		}

		remote_conn_details = malloc(remote_conn_details_len);
		if (!remote_conn_details) {
			DOCA_LOG_ERR("Failed to allocate memory for connection details");
			result = DOCA_ERROR_NO_MEMORY;
			goto cleanup_connection;
		}

		if (recv(oob_sock_fd, remote_conn_details, remote_conn_details_len, 0) < 0) {
			DOCA_LOG_ERR("Failed to receive connection details");
			result = DOCA_ERROR_CONNECTION_ABORTED;
			free(remote_conn_details);
			goto cleanup_connection;
		}

		/* Send local connection details */
		if (send(oob_sock_fd, &resources.conn_det_len, sizeof(size_t), 0) < 0) {
			DOCA_LOG_ERR("Failed to send connection details length");
			result = DOCA_ERROR_CONNECTION_ABORTED;
			free(remote_conn_details);
			goto cleanup_connection;
		}

		if (send(oob_sock_fd, resources.connection_details, resources.conn_det_len, 0) < 0) {
			DOCA_LOG_ERR("Failed to send connection details");
			result = DOCA_ERROR_CONNECTION_ABORTED;
			free(remote_conn_details);
			goto cleanup_connection;
		}

		/* Connect to server */
		result = doca_rdma_connect(resources.rdma, remote_conn_details,
					   remote_conn_details_len, connection);
		free(remote_conn_details);
		if (result != DOCA_SUCCESS) {
			DOCA_LOG_ERR("Failed to connect to server: %s", doca_error_get_descr(result));
			goto cleanup_connection;
		}
	}

	/* Create local memory for bandwidth test */
	result = create_write_bw_memory(&resources, &cfg->write_bw, &local_mmap, &local_buf_arr, false);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to create local memory: %s", doca_error_get_descr(result));
		goto cleanup_connection;
	}

	/* Exchange memory mappings with server */
	result = exchange_memory_mappings(oob_sock_fd, &local_mmap, &remote_mmap, &resources, false);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to exchange memory mappings: %s", doca_error_get_descr(result));
		goto cleanup_memory;
	}

	/* Create remote buffer array */
	remote_buf_arr.gpudev = resources.gpudev;
	remote_buf_arr.mmap = remote_mmap;
	remote_buf_arr.num_elem = GPU_BUF_NUM;
	remote_buf_arr.elem_size = local_buf_arr.elem_size;

	result = create_buf_arr_on_gpu(&remote_buf_arr);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to create remote buffer array: %s", doca_error_get_descr(result));
		goto cleanup_memory;
	}

	/* Run the bandwidth test */
	result = run_write_bw_test(&resources, &local_buf_arr, &remote_buf_arr,
				   &cfg->write_bw, &metrics);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Bandwidth test failed: %s", doca_error_get_descr(result));
		goto cleanup_remote_buf;
	}

	/* Print results */
	print_write_bw_results(&cfg->write_bw, &metrics);

	/* Send completion signal to server */
	char completion_signal = 1;
	if (send(oob_sock_fd, &completion_signal, 1, 0) < 0) {
		DOCA_LOG_WARN("Failed to send completion signal to server");
	}

	DOCA_LOG_INFO("RDMA Write Bandwidth Test completed successfully");

cleanup_remote_buf:
	if (remote_buf_arr.buf_arr) {
		doca_buf_arr_destroy(remote_buf_arr.buf_arr);
	}

cleanup_memory:
	if (remote_mmap) {
		doca_mmap_destroy(remote_mmap);
	}
	if (local_buf_arr.buf_arr) {
		doca_buf_arr_destroy(local_buf_arr.buf_arr);
	}
	if (local_mmap.mmap) {
		doca_mmap_destroy(local_mmap.mmap);
	}
	if (local_mmap.memrange_addr) {
		doca_gpu_mem_free(resources.gpudev, local_mmap.memrange_addr);
	}

cleanup_connection:
	if (oob_sock_fd >= 0) {
		oob_connection_client_close(oob_sock_fd);
	}

cleanup_resources:
	destroy_rdma_resources(&resources);

	return result;
}

/*
 * RDMA Write bandwidth test - Server side implementation
 */
doca_error_t rdma_write_bw_test_server(struct rdma_config *cfg)
{
	struct rdma_resources resources = {0};
	struct rdma_mmap_obj local_mmap = {0};
	struct buf_arr_obj local_buf_arr = {0};
	struct doca_mmap *remote_mmap = NULL;
	doca_error_t result;
	int oob_sock_fd = -1, oob_client_sock = -1;
	const uint32_t rdma_permissions = DOCA_ACCESS_FLAG_LOCAL_READ_WRITE | DOCA_ACCESS_FLAG_RDMA_WRITE;

	/* Set default configuration if not set */
	if (!cfg->enable_write_bw_test) {
		set_default_write_bw_config(&cfg->write_bw);
		cfg->enable_write_bw_test = true;
	}

	DOCA_LOG_INFO("Starting RDMA Write Bandwidth Test - Server");
	DOCA_LOG_INFO("Waiting for client connection...");

	/* Create RDMA resources */
	result = create_rdma_resources(cfg, rdma_permissions, &resources);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to create RDMA resources: %s", doca_error_get_descr(result));
		return result;
	}

	/* Get GPU RDMA handle */
	result = doca_rdma_get_gpu_handle(resources.rdma, &resources.gpu_rdma);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to get GPU RDMA handle: %s", doca_error_get_descr(result));
		goto cleanup_resources;
	}

	/* Setup OOB connection for client */
	if (oob_connection_server_setup(&oob_sock_fd, &oob_client_sock) < 0) {
		DOCA_LOG_ERR("Failed to setup OOB connection");
		result = DOCA_ERROR_CONNECTION_ABORTED;
		goto cleanup_resources;
	}

	DOCA_LOG_INFO("Client connected, establishing RDMA connection...");

	/* Establish RDMA connection */
	if (!cfg->use_rdma_cm) {
		/* Use OOB connection method */
		void *remote_conn_details = NULL;
		size_t remote_conn_details_len = 0;
		struct doca_rdma_connection *connection = NULL;

		/* Export local connection details */
		result = doca_rdma_export(resources.rdma, &resources.connection_details,
					  &resources.conn_det_len, &connection);
		if (result != DOCA_SUCCESS) {
			DOCA_LOG_ERR("Failed to export RDMA connection: %s", doca_error_get_descr(result));
			goto cleanup_connection;
		}

		/* Send connection details to client */
		if (send(oob_client_sock, &resources.conn_det_len, sizeof(size_t), 0) < 0) {
			DOCA_LOG_ERR("Failed to send connection details length");
			result = DOCA_ERROR_CONNECTION_ABORTED;
			goto cleanup_connection;
		}

		if (send(oob_client_sock, resources.connection_details, resources.conn_det_len, 0) < 0) {
			DOCA_LOG_ERR("Failed to send connection details");
			result = DOCA_ERROR_CONNECTION_ABORTED;
			goto cleanup_connection;
		}

		/* Receive client connection details */
		if (recv(oob_client_sock, &remote_conn_details_len, sizeof(size_t), 0) < 0) {
			DOCA_LOG_ERR("Failed to receive connection details length");
			result = DOCA_ERROR_CONNECTION_ABORTED;
			goto cleanup_connection;
		}

		remote_conn_details = malloc(remote_conn_details_len);
		if (!remote_conn_details) {
			DOCA_LOG_ERR("Failed to allocate memory for connection details");
			result = DOCA_ERROR_NO_MEMORY;
			goto cleanup_connection;
		}

		if (recv(oob_client_sock, remote_conn_details, remote_conn_details_len, 0) < 0) {
			DOCA_LOG_ERR("Failed to receive connection details");
			result = DOCA_ERROR_CONNECTION_ABORTED;
			free(remote_conn_details);
			goto cleanup_connection;
		}

		/* Connect to client */
		result = doca_rdma_connect(resources.rdma, remote_conn_details,
					   remote_conn_details_len, connection);
		free(remote_conn_details);
		if (result != DOCA_SUCCESS) {
			DOCA_LOG_ERR("Failed to connect to client: %s", doca_error_get_descr(result));
			goto cleanup_connection;
		}
	}

	/* Create local memory for bandwidth test */
	result = create_write_bw_memory(&resources, &cfg->write_bw, &local_mmap, &local_buf_arr, true);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to create local memory: %s", doca_error_get_descr(result));
		goto cleanup_connection;
	}

	/* Exchange memory mappings with client */
	result = exchange_memory_mappings(oob_client_sock, &local_mmap, &remote_mmap, &resources, true);
	if (result != DOCA_SUCCESS) {
		DOCA_LOG_ERR("Failed to exchange memory mappings: %s", doca_error_get_descr(result));
		goto cleanup_memory;
	}

	DOCA_LOG_INFO("RDMA connection established, memory mappings exchanged");
	DOCA_LOG_INFO("Server ready for bandwidth test. Waiting for client to start...");

	/* Server just waits - the client will perform the write operations */
	/* In a write bandwidth test, the client writes to server memory */
	/* Server can optionally monitor or validate the received data */

	/* Wait for a signal from client that test is complete */
	char completion_signal;
	if (recv(oob_client_sock, &completion_signal, 1, 0) < 0) {
		DOCA_LOG_WARN("Failed to receive completion signal from client");
	}

	DOCA_LOG_INFO("Bandwidth test completed");

cleanup_memory:
	if (remote_mmap) {
		doca_mmap_destroy(remote_mmap);
	}
	if (local_buf_arr.buf_arr) {
		doca_buf_arr_destroy(local_buf_arr.buf_arr);
	}
	if (local_mmap.mmap) {
		doca_mmap_destroy(local_mmap.mmap);
	}
	if (local_mmap.memrange_addr) {
		doca_gpu_mem_free(resources.gpudev, local_mmap.memrange_addr);
	}

cleanup_connection:
	if (oob_sock_fd >= 0) {
		oob_connection_server_close(oob_sock_fd, oob_client_sock);
	}

cleanup_resources:
	destroy_rdma_resources(&resources);

	return result;
}
