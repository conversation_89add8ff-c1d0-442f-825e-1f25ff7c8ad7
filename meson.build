#
# Copyright (c) 2023-2024 NVIDIA CORPORATION AND AFFILIATES.  All rights reserved.
#
# Redistribution and use in source and binary forms, with or without modification, are permitted
# provided that the following conditions are met:
#     * Redistributions of source code must retain the above copyright notice, this list of
#       conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above copyright notice, this list of
#       conditions and the following disclaimer in the documentation and/or other materials
#       provided with the distribution.
#     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used
#       to endorse or promote products derived from this software without specific prior written
#       permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR
# IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
# FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE
# FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL, <PERSON>XEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING,
# BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
# OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
# STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#

project('DOCA_SAMPLE', 'C', 'CPP', 'CUDA',
	# Get version number from file.
	version: run_command(find_program('cat'),
		files('/opt/mellanox/doca/applications/VERSION'), check: true).stdout().strip(),
	license: 'Proprietary',
	default_options: ['buildtype=release'],
	meson_version: '>= 0.61.2'
)

SAMPLE_NAME = 'gpunetio_rdma_client_server_write'

# Comment this line to restore warnings of experimental DOCA features
add_project_arguments('-D DOCA_ALLOW_EXPERIMENTAL_API', language: ['c', 'cpp', 'cuda'])

sample_dependencies = []
# Required for all DOCA programs
sample_dependencies += dependency('doca-common')
# The DOCA library of the sample itself
doca_gpu_dep = dependency('doca-gpunetio')
sample_dependencies += doca_gpu_dep
# Utility DOCA library for executables
sample_dependencies += dependency('doca-argp')
# Additional DOCA library that is relevant for this sample
sample_dependencies += dependency('doca-rdma')
# Required DOCA Driver
sample_dependencies += dependency('libdpdk')

cuda = import('unstable-cuda')
nvcc = meson.get_compiler('cuda')

nvcc_flags = []
nvcc_flags += ['-gencode', 'arch=compute_70,code=sm_70']
add_project_arguments(nvcc_flags, language: 'cuda')

# Refer to https://mesonbuild.com/Cuda-module.html
add_project_arguments('-forward-unknown-to-host-compiler', language: 'cuda')
add_project_arguments('-rdc=true', language: 'cuda')

gpu_dependencies = []
gpu_dependencies += dependency('cuda', version: '>= 12-1', modules: ['cudart', 'cuda'])

nvcc_flags_link = []
nvcc_flags_link += ['-gencode=arch=compute_70,code=sm_70']
add_project_link_arguments(nvcc_flags_link, language: 'cuda')

sample_dependencies += gpu_dependencies

gpunetio_device_path = doca_gpu_dep.get_variable(pkgconfig : 'libdir')
dependency_gpunetio_device = declare_dependency(compile_args : '-Wl,--whole-archive',
                                                link_args : ['-L' + gpunetio_device_path , '-ldoca_gpunetio_device'],)
sample_dependencies += dependency_gpunetio_device

sample_srcs = [
	# The sample itself
	'device/' + SAMPLE_NAME + '_kernel.cu',
	'host/' + SAMPLE_NAME + '_sample.c',
	# Main function for the sample's executable
	SAMPLE_NAME + '_main.c',
	# Common code for RDMA-based GPUNetIO samples
	'rdma_common.c',
	# Common code for all DOCA samples
	'common.c',
	# RDMA write bandwidth test implementation
	'rdma_write_bw_test.c',
]

sample_inc_dirs  = ['.']


executable('doca_' + SAMPLE_NAME, sample_srcs,
	c_args : '-Wno-missing-braces',
	dependencies : sample_dependencies,
	include_directories: sample_inc_dirs,
	install: false)
