# RDMA Write Bandwidth Test - Usage Example

This document provides step-by-step instructions for running the RDMA write bandwidth test.

## Prerequisites

1. **Hardware Requirements:**
   - Two machines with NVIDIA GPUs
   - InfiniBand network connection between machines
   - NVIDIA DOCA framework installed

2. **Software Requirements:**
   - CUDA toolkit
   - DOCA libraries
   - Build tools (meson, ninja)

## Step 1: Build the Project

```bash
# Clone or navigate to the project directory
cd /path/to/gpunetio_test

# Build the project
./build.sh

# Verify the executable was created
ls -la build/doca_gpunetio_rdma_client_server_write
```

## Step 2: Identify Hardware

### Find InfiniBand Device
```bash
# List available InfiniBand devices
ibstat

# Example output:
# CA 'mlx5_0'
#     CA type: MT4123
#     Number of ports: 1
#     Firmware version: 20.31.1014
#     Hardware version: 0
```

### Find GPU PCI Address
```bash
# List GPU devices
nvidia-smi -L

# Get detailed PCI information
lspci | grep -i nvidia

# Example output:
# 01:00.0 3D controller: NVIDIA Corporation GA100 [A100 PCIe 40GB] (rev a1)
```

## Step 3: Configure Network

### Server Machine (*************)
```bash
# Ensure InfiniBand interface is up
sudo ip link set dev ib0 up
sudo ip addr add *************/24 dev ib0

# Verify connectivity
ping *************  # Client IP
```

### Client Machine (*************)
```bash
# Configure InfiniBand interface
sudo ip link set dev ib0 up
sudo ip addr add *************/24 dev ib0

# Verify connectivity
ping *************  # Server IP
```

## Step 4: Run Basic Test

### Terminal 1 (Server - *************)
```bash
cd /path/to/gpunetio_test

# Start server with bandwidth test
./build/doca_gpunetio_rdma_client_server_write \
    --device mlx5_0 \
    --gpu 0000:01:00.0 \
    --write-bw-test \
    --message-size 4096 \
    --iterations 1000 \
    --verbose
```

### Terminal 2 (Client - *************)
```bash
cd /path/to/gpunetio_test

# Start client with bandwidth test
./build/doca_gpunetio_rdma_client_server_write \
    --device mlx5_0 \
    --gpu 0000:01:00.0 \
    --client ************* \
    --write-bw-test \
    --message-size 4096 \
    --iterations 1000 \
    --verbose
```

## Step 5: Expected Output

### Server Output
```
[INFO] Starting RDMA Write Bandwidth Test - Server
[INFO] Waiting for client connection...
[INFO] Client connected, establishing RDMA connection...
[INFO] RDMA connection established, memory mappings exchanged
[INFO] Server ready for bandwidth test. Waiting for client to start...
[INFO] Bandwidth test completed
```

### Client Output
```
[INFO] Starting RDMA Write Bandwidth Test - Client
[INFO] Message size: 4096 bytes, Iterations: 1000
[INFO] Starting RDMA write bandwidth test...
[INFO] Running 100 warmup iterations...
[INFO] Running main test with 1000 iterations...
[INFO] Test completed successfully

=== RDMA Write Bandwidth Test Results ===
Configuration:
  Message Size: 4096 bytes
  Iterations: 1000
  Warmup Iterations: 100
  CUDA Blocks: 1
  Threads per Block: 32

Results:
  Bandwidth: 45.67 Gbps
  Total Data: 3.91 MB
  Total Operations: 1000
  Test Duration: 0.685 seconds
  Average Latency: 2.34 μs
  Min Latency: 1.89 μs
  Max Latency: 4.56 μs
==========================================

[INFO] RDMA Write Bandwidth Test completed successfully
```

## Step 6: Advanced Testing

### Test Different Message Sizes
```bash
# Test with 1KB messages
./build/doca_gpunetio_rdma_client_server_write \
    --device mlx5_0 \
    --gpu 0000:01:00.0 \
    --client ************* \
    --write-bw-test \
    --message-size 1024 \
    --iterations 5000

# Test with 64KB messages
./build/doca_gpunetio_rdma_client_server_write \
    --device mlx5_0 \
    --gpu 0000:01:00.0 \
    --client ************* \
    --write-bw-test \
    --message-size 65536 \
    --iterations 1000
```

### Use the Automated Test Script
```bash
# Make script executable
chmod +x test_write_bw.sh

# Edit script configuration
vim test_write_bw.sh
# Update DEVICE_NAME, GPU_PCI_ADDR, SERVER_IP

# Run comprehensive test
./test_write_bw.sh --comprehensive

# Run single test with custom size
./test_write_bw.sh --single 16384
```

## Step 7: Troubleshooting

### Common Issues and Solutions

1. **"Failed to create RDMA resources"**
   ```bash
   # Check InfiniBand device status
   ibstat
   
   # Verify device name is correct
   ls /sys/class/infiniband/
   ```

2. **"Failed to allocate GPU memory"**
   ```bash
   # Check GPU memory usage
   nvidia-smi
   
   # Free GPU memory if needed
   sudo fuser -v /dev/nvidia*
   ```

3. **"Failed to setup OOB connection"**
   ```bash
   # Check network connectivity
   ping <server_ip>
   
   # Verify firewall settings
   sudo iptables -L
   
   # Check if port is in use
   netstat -tulpn | grep :13579
   ```

4. **"CUDA kernel launch failed"**
   ```bash
   # Check CUDA installation
   nvcc --version
   
   # Verify GPU compatibility
   nvidia-smi
   
   # Check CUDA samples
   cd /usr/local/cuda/samples/1_Utilities/deviceQuery
   make && ./deviceQuery
   ```

### Debug Mode

Enable detailed logging:
```bash
export DOCA_LOG_LEVEL=20  # Set to DEBUG level

# Run with verbose output
./build/doca_gpunetio_rdma_client_server_write \
    --device mlx5_0 \
    --gpu 0000:01:00.0 \
    --client ************* \
    --write-bw-test \
    --verbose
```

### Performance Tuning

1. **Optimize Message Size:**
   - Start with 4KB for latency-sensitive applications
   - Use 64KB+ for maximum bandwidth
   - Test multiple sizes to find optimal point

2. **Adjust Iterations:**
   - More iterations = better statistical accuracy
   - Fewer iterations = faster test completion
   - Recommended: 1000-10000 iterations

3. **CUDA Configuration:**
   - Adjust blocks/threads based on GPU capabilities
   - Monitor GPU utilization with nvidia-smi
   - Consider multiple streams for higher throughput

## Step 8: Interpreting Results

### Bandwidth Analysis
- **Good Performance:** >80% of theoretical InfiniBand bandwidth
- **Moderate Performance:** 50-80% of theoretical bandwidth
- **Poor Performance:** <50% of theoretical bandwidth

### Latency Analysis
- **Excellent:** <2 μs average latency
- **Good:** 2-5 μs average latency
- **Acceptable:** 5-10 μs average latency
- **Poor:** >10 μs average latency

### Factors Affecting Performance
- Message size (larger = higher bandwidth, higher latency)
- Network congestion
- GPU memory bandwidth
- CPU load
- System configuration

## Next Steps

1. **Baseline Testing:** Establish performance baselines for your hardware
2. **Optimization:** Tune parameters for your specific use case
3. **Integration:** Incorporate bandwidth testing into your applications
4. **Monitoring:** Set up regular performance monitoring
5. **Scaling:** Test with multiple connections or larger deployments
