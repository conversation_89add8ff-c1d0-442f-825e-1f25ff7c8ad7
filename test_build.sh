#!/bin/bash

# Test build script for RDMA Write Bandwidth Test
# This script tests if the implementation compiles correctly

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "Testing RDMA Write Bandwidth Test build..."

# Check if build directory exists
if [ ! -d "build" ]; then
    print_info "Creating build directory..."
    mkdir build
fi

# Check if meson.build exists
if [ ! -f "meson.build" ]; then
    print_error "meson.build not found. Are you in the correct directory?"
    exit 1
fi

# Check for required source files
required_files=(
    "gpunetio_rdma_client_server_write_main.c"
    "rdma_common.h"
    "rdma_common.c"
    "rdma_write_bw_test.c"
    "device/gpunetio_rdma_client_server_write_kernel.cu"
    "host/gpunetio_rdma_client_server_write_sample.c"
    "common.h"
    "common.c"
)

print_info "Checking required source files..."
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "Required file not found: $file"
        exit 1
    else
        print_info "✓ Found: $file"
    fi
done

# Check for DOCA installation
print_info "Checking DOCA installation..."
if ! pkg-config --exists doca-common; then
    print_warning "DOCA development packages not found via pkg-config"
    print_warning "Make sure DOCA is properly installed"
fi

# Check for CUDA installation
print_info "Checking CUDA installation..."
if ! command -v nvcc &> /dev/null; then
    print_warning "CUDA compiler (nvcc) not found in PATH"
    print_warning "Make sure CUDA toolkit is properly installed"
fi

# Try to configure with meson
print_info "Configuring build with meson..."
cd build

if meson setup .. --reconfigure; then
    print_info "✓ Meson configuration successful"
else
    print_error "✗ Meson configuration failed"
    print_error "Check that all dependencies are installed:"
    echo "  - NVIDIA DOCA framework"
    echo "  - CUDA toolkit"
    echo "  - DPDK libraries"
    echo "  - Meson build system"
    exit 1
fi

# Try to compile
print_info "Attempting to compile..."
if ninja; then
    print_info "✓ Compilation successful!"
    
    # Check if executable was created
    if [ -f "doca_gpunetio_rdma_client_server_write" ]; then
        print_info "✓ Executable created successfully"
        
        # Show executable info
        ls -la doca_gpunetio_rdma_client_server_write
        
        # Test help output
        print_info "Testing help output..."
        if ./doca_gpunetio_rdma_client_server_write --help > /dev/null 2>&1; then
            print_info "✓ Executable runs and shows help"
        else
            print_warning "Executable created but help command failed"
            print_warning "This might be due to missing runtime dependencies"
        fi
        
        # Show available parameters
        print_info "Available parameters:"
        ./doca_gpunetio_rdma_client_server_write --help 2>/dev/null | grep -E "^\s*-" | head -10
        
    else
        print_error "✗ Executable not found after compilation"
        exit 1
    fi
else
    print_error "✗ Compilation failed"
    print_error "Check the error messages above for details"
    exit 1
fi

cd ..

print_info "Build test completed successfully!"
print_info ""
print_info "Next steps:"
print_info "1. Configure your hardware settings in test_write_bw.sh"
print_info "2. Run the bandwidth test:"
print_info "   Server: ./build/doca_gpunetio_rdma_client_server_write --device <dev> --gpu <gpu> --write-bw-test"
print_info "   Client: ./build/doca_gpunetio_rdma_client_server_write --device <dev> --gpu <gpu> --client <ip> --write-bw-test"
print_info "3. Or use the automated test script: ./test_write_bw.sh --single"
